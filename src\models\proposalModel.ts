import { Schema, Model, model } from "mongoose";
import { IProposalModel } from "./Interfaces/proposalInterface";


const quotationSchema = new Schema(
  {
    contractTypeId: { type: Schema.Types.ObjectId },
    price: { type: Number },
    quantity: { type: Number },
    details: { type: String }
  },
  {
    timestamps: true,
  }
);
const proposalSchema = new Schema(
  {
    itemId: { type: Schema.Types.ObjectId },
    active:{type:Boolean, default:true},
    subject:{type:String},
    termsAndCondition:{type:String},
    // contractTypeId: { type: Schema.Types.ObjectId },
    // details: { type: String },
    // price: { type: Number },
    // quantity: { type: Number },
    quotations: [quotationSchema],
    status: { type:String, enum:['draft','sent'] },
    createdById: { type: Schema.Types.ObjectId },
    updatedById: { type: Schema.Types.ObjectId }
  },
  {
    timestamps: true,
  }
);

export const proposalModel: Model<IProposalModel> = model<IProposalModel>(
  "proposal",
  proposalSchema
);
