import App from './helpers/appServer';
import config from './config/appConfig';
import * as bodyParser from 'body-parser';
import cors = require('cors');
import DefaultRoute from './routes/defaultRoute';
import dbConnection from './db/connection';
import AuthMiddleWare from './middleware/authMiddleware';
import * as cluster from 'cluster'; 
import ItemRoute from './routes/itemRoute';  
import ProposalRoute from './routes/proposalRoute';  
import ContractRoute from './routes/contractRoute';  
import MailRoute from './routes/mailRoute';


const bootServer = async () => {
  await dbConnection(); 
  const app = new App({
    port: config.app.port,
    defaults: [cors({
      origin: '*', // Update as needed for security
      methods: ['GET,HEAD,PUT,PATCH,POST,DELETE'],
      preflightContinue: false,
      optionsSuccessStatus: 204
  })],
    middleWares: [
      bodyParser.json({ limit: '5mb' }),
      bodyParser.urlencoded({ extended: true }),
      AuthMiddleWare
    ],
    routes: [
      new DefaultRoute(),
      new ItemRoute(),
      new ProposalRoute(),
      new ContractRoute(),
      new MailRoute()
    ]
  });
  app.listen();
};

if ((cluster as any).isPrimary || (cluster as any).isMaster) {
  let a = 1;
  console.log('server is running through cluster');
  for (let i = 0; i < a; i++) {
    (cluster as any).fork();
  }
  (cluster as any).on('exit', (worker: any) => {
    console.log(`worker ${worker.process.pid} died`);
  });
} else {
  bootServer();
}