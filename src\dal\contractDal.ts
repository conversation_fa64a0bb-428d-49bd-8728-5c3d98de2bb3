import { contractModel } from '../models/contractModel'
import { itemModel } from '../models/itemModel'
import { proposalModel } from '../models/proposalModel';
import AppHelper from '../helpers/appHelper'
import { Types } from 'mongoose';
import { connection } from 'mongoose';


export default class ContractDal {

  public appHelper: any;
  public packageHelper: any;
  constructor() {
    this.appHelper = new AppHelper;
  }


  public createContract = async (data: any): Promise<any> => {
    try {

      let model = new contractModel(data)
      let result = await model.save()
      if (result) {

          if (data.proposalId) {
        await proposalModel.findByIdAndUpdate(
          data.proposalId,
          { $set: { active: false } },
          { new: true }
        )
      }
        return { status: true, code: 200, message: "Data saved successfully", data: result._id }
      }
      return { status: false, code: 403, message: `Failed to save data`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }



// DAL Layer
public getDashboardStats = async (req: any) => {
  try {

    const userModel = connection.db.collection('users');
    const ticketModel = connection.db.collection('tickets');
    
    // Get counts from all collections in parallel
    const [
      totalItems,
      totalCustomers,
      totalEmployees,
      totalContracts,
      totalTickets
    ] = await Promise.all([
      itemModel.countDocuments(),
      userModel.countDocuments({ userType: "user" }),
      userModel.countDocuments({ userType: "employee" }),
      contractModel.countDocuments(),
      ticketModel.countDocuments()
    ]);

    const stats = {
      totalItems,
      totalCustomers,
      totalEmployees,
      totalContracts,
      totalTickets
    };

    return {
      status: true,
      code: 200,
      message: "Dashboard stats fetched successfully",
      data: stats
    };

  } catch (error: any) {
    return {
      status: false,
      code: 500,
      message: "Error fetching dashboard stats",
      data: error.message
    };
  }
};
public getTotalContractsByFilter = async (req: any) => {
  try {
    const { filter } = req.query; // 'monthly', 'weekly', or 'daily'
    
    const now = new Date();
    let pipeline: any[] = [];
    let formattedData: any[] = [];

    if (filter === 'monthly') {
      // Last 12 months
      const startDate = new Date(now.getFullYear(), 0, 1); // Start of current year
      
      pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: { $month: "$createdAt" },
            value: { $sum: 1 }
          }
        },
        {
          $sort: { "_id": 1 }
        }
      ];

      const data = await contractModel.aggregate(pipeline);
      
      // Create array with all 12 months
      const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      const monthMap = new Map(data.map(item => [item._id, item.value]));
      
      formattedData = monthNames.map((month, index) => ({
        month: month,
        value: monthMap.get(index + 1) || 0
      }));

    } else if (filter === 'weekly') {
      // Last 8 weeks
      const startDate = new Date(now.getTime() - (8 * 7 * 24 * 60 * 60 * 1000));
      
      pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              year: { $year: "$createdAt" },
              week: { $week: "$createdAt" }
            },
            value: { $sum: 1 },
            minDate: { $min: "$createdAt" }
          }
        },
        {
          $sort: { "minDate": 1 }
        },
        {
          $project: {
            _id: 0,
            value: 1,
            weekNum: "$_id.week"
          }
        }
      ];

      const data = await contractModel.aggregate(pipeline);
      
      formattedData = data.map((item, index) => ({
        week: `Week ${index + 1}`,
        value: item.value
      }));

    } else if (filter === 'daily') {
      // Last 7 days
      const startDate = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
      startDate.setHours(0, 0, 0, 0);
      
      pipeline = [
        {
          $match: {
            createdAt: { $gte: startDate }
          }
        },
        {
          $group: {
            _id: {
              $dateToString: {
                format: "%Y-%m-%d",
                date: "$createdAt"
              }
            },
            value: { $sum: 1 },
            date: { $first: "$createdAt" }
          }
        },
        {
          $sort: { "date": 1 }
        },
        {
          $project: {
            _id: 0,
            dayOfWeek: { $dayOfWeek: "$date" },
            value: 1
          }
        }
      ];

      const data = await contractModel.aggregate(pipeline);
      
      
      // Map dayOfWeek number to day name (1=Sunday, 2=Monday, etc.)
      const dayMapping: { [key: number]: string } = {
        1: 'Sun',
        2: 'Mon',
        3: 'Tue',
        4: 'Wed',
        5: 'Thu',
        6: 'Fri',
        7: 'Sat'
      };
      
      // Ensure we have all 7 days
      const dayNames = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      const dayMap = new Map(
        data.map(item => [dayMapping[item.dayOfWeek], item.value])
      );

    formattedData = dayNames.map(day => ({
        day: day,
        value: dayMap.get(day) || 0
      }));

    } else {
      return {
        status: false,
        code: 400,
        message: "Invalid filter. Use 'monthly', 'weekly', or 'daily'",
        data: null
      };
    }

    return {
      status: true,
      code: 200,
      message: "Contracts data fetched successfully",
      data: formattedData
    };

  } catch (error: any) {
    return {
      status: false,
      code: 500,
      message: "Error fetching Contract data",
      data: error.message
    };
  }
};

  public updateContract = async (data: any, id: any): Promise<any> => {
    try {
      const find = { _id: id }
      const set = { $set: data }
      const options = { upsert: false, new: true }
      let result = await contractModel.findOneAndUpdate(find, set, options).exec()
      if (result) {
        return { status: true, code: 200, message: 'Data updated successfully', data: result }
      }
      return { status: false, code: 409, message: `Failed to update brand`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }

  }



  public getContractById = async (id: string): Promise<any> => {
    try {
        let query:any =[{}]

              // Calculate date one month before current date for nearExpiry status
      const currentDate = new Date();
      const oneMonthFromNow = new Date();
      oneMonthFromNow.setMonth(currentDate.getMonth() + 1);

        query.push({_id: new Types.ObjectId(id)})
      let result: any = await contractModel.aggregate()
      .match({$and: query})
      .lookup({
        from: 'items',
        localField: 'itemId',
        foreignField: '_id',
        as: 'itemDetail',
      })
      .unwind({
        path: '$itemDetail',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
      .lookup({
        from: 'brands',
        localField: 'itemDetail.brandId',
        foreignField: '_id',
        as: 'brandDetail',
      })
      .unwind({
        path: '$brandDetail',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        from: 'products',
        localField: 'itemDetail.productId',
        foreignField: '_id',
        as: 'productDetail',
      })
      .unwind({
        path: '$productDetail',
        preserveNullAndEmptyArrays: true,
      })
      .lookup({
        from: 'models',
        localField: 'itemDetail.modelId',
        foreignField: '_id',
        as: 'modelDetail',
      })
      .unwind({
        path: '$modelDetail',
        preserveNullAndEmptyArrays: true,
      })
       .lookup({
          from: 'quotations',
          localField: 'contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractquotationDetail',
        })
        .unwind({
          path: '$contractquotationDetail',
          preserveNullAndEmptyArrays: true,
        })
      .collation({ locale: "en" })
      .project({
        _id: 1,
        itemDetail: 1,
        customerDetail: 1,
        brandName: '$brandDetail.brandName',
        productName: '$productDetail.productTypeName',
        modelName: '$modelDetail.modelName',
        customerAddress: {
          $arrayElemAt: [
            {
              $filter: {
                input: "$customerDetail.customerAddress",
                as: "address",
                cond: { $eq: ["$$address.status", true] }
              }
            },
            0
          ]
        },
        status: {
            $cond: {
              // First check if startDate is after current date (pending)
              if: { $gt: ["$startDate", currentDate] },
              then: "pending",
              else: {
                $cond: {
                  // Check if current date is after endDate (expired)
                  if: { $gt: [currentDate, "$endDate"] },
                  then: "expired",
                  else: {
                    $cond: {
                      // Check if endDate is within 1 month from now (nearExpiry)
                      if: {
                        $and: [
                          { $lte: [currentDate, "$endDate"] },
                          { $lte: ["$endDate", oneMonthFromNow] }
                        ]
                      },
                      then: "nearExpiry",
                      else: {
                        $cond: {
                          // Check if current date is between startDate and endDate (active)
                          if: {
                            $and: [
                              { $gte: [currentDate, "$startDate"] },
                              { $lte: [currentDate, "$endDate"] }
                            ]
                          },
                          then: "active",
                          else: "pending" // fallback
                        }
                      }
                    }
                  }
                }
              }
            }
        },
        startDate: 1,
        endDate: 1,
        quantity: 1,
        price: 1,
        contractType:'$contractquotationDetail.name',
      })
      .exec()
      if (result) {

        return { status: true, code: 200, message: 'Data fetched successfully', data: result }
      }
      return { status: true, code: 200, message: 'No such document!', data: [] }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }


  public getContract = async (req: any): Promise<any> => {
    try {
      let offset = req.query.offset ? req.query.offset : 0;
      let limit = req.query.limit ? req.query.limit : 15;
      let query = [{}]
      let specialField: string = req.query.specialField; // new parameter for specialized search
      
      if (req.query.search) {

        let searchValue: any = []
        let value = req.query.search;
        let globalValue = String(value).replace(/([.*+?=^!:${}()|[\]\/\\])/g, '\\$1');

        if (specialField && req.query.type === "ticket") {
          if (specialField === 'customerName') {
            searchValue.push({ 'customerName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          } else if (specialField === 'model') {
            searchValue.push({ 'modelName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'brand') {
            searchValue.push({ 'brandName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'product') {
            searchValue.push({ 'productName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'serialNo') {
            searchValue.push({ 'serialNo': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          }
          else if (specialField === 'phone' && !isNaN(value)) {
            // Search in concatenated countryCode + number for special field
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      { $toString: "$phone.countryCode" },
                      { $toString: "$phone.number" }
                    ]
                  },
                  regex: parseInt(value).toString(),
                  options: "i"
                }
              }
            });
          }
        } else if(req.query.type === "ticket") {
          // If specialField is invalid, default to normal search behavior
          searchValue.push({ 'serialNo': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'customerName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'brandName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'modelName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'productName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          if (!isNaN(value)) {
            // Search in concatenated countryCode + number
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      { $toString: "$phone.countryCode" },
                      { $toString: "$phone.number" }
                    ]
                  },
                  regex: parseInt(value).toString(),
                  options: "i"
                }
              }
            });

            // Also search in number field only (fallback)
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: { $toString: "$phone.number" },
                  regex: "^" + parseInt(value),
                  options: "i"
                }
              }
            });
          }
        }
        else {
          searchValue.push({ 'serialNo': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          searchValue.push({ 'customerName': { $regex: '.*' + globalValue + '.*', $options: 'i' } });
          if (!isNaN(value)) {
            // Search in concatenated countryCode + number
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: {
                    $concat: [
                      { $toString: "$phone.countryCode" },
                      { $toString: "$phone.number" }
                    ]
                  },
                  regex: parseInt(value).toString(),
                  options: "i"
                }
              }
            });

            // Also search in number field only (fallback)
            searchValue.push({
              $expr: {
                $regexMatch: {
                  input: { $toString: "$phone.number" },
                  regex: "^" + parseInt(value),
                  options: "i"
                }
              }
            });
          }
        }


        query.push({ $or: searchValue });

      }

      if (req.query.status) {
        query.push({
          status: req.query.status
        })
      }

      if (req.query.contractType) {
        query.push({
          contractTypeId: new Types.ObjectId(req.query.contractType)
        })
      }

      // Calculate date one month before current date for nearExpiry status
      const currentDate = new Date();
      const oneMonthFromNow = new Date();
      oneMonthFromNow.setMonth(currentDate.getMonth() + 1);

      let result: any = await contractModel.aggregate()
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        // Add this stage to extract the active customer address
        .addFields({
          customerAddress: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$customerDetail.customerAddress',
                  cond: { $eq: ['$$this.status', true] }
                }
              },
              0
            ]
          }
        })
        .lookup({
          from: 'quotations',
          localField: 'contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractquotationDetail',
        })
        .unwind({
          path: '$contractquotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: '$itemDetail.serialNo',
          customerName: '$customerDetail.fullName',
          phone: '$customerDetail.phone',
          email: '$customerDetail.email',
          telePhoneNo: '$customerDetail.alternatePhone',
          address: "$itemDetail.installationAddress",
          customerAddress: 1,
          assignmentDetail: '$itemDetail.assignmentDetail',
          installationDate: '$itemDetail.installationDate',
          warrantyStartDate: '$itemDetail.warrantyStartDate',
          warrantyEndDate: '$itemDetail.warrantyEndDate',
          // Dynamic status calculation
          status: {
            $cond: {
              // First check if startDate is after current date (pending)
              if: { $gt: ["$startDate", currentDate] },
              then: "pending",
              else: {
                $cond: {
                  // Check if current date is after endDate (expired)
                  if: { $gt: [currentDate, "$endDate"] },
                  then: "expired",
                  else: {
                    $cond: {
                      // Check if endDate is within 1 month from now (nearExpiry)
                      if: {
                        $and: [
                          { $lte: [currentDate, "$endDate"] },
                          { $lte: ["$endDate", oneMonthFromNow] }
                        ]
                      },
                      then: "nearExpiry",
                      else: {
                        $cond: {
                          // Check if current date is between startDate and endDate (active)
                          if: {
                            $and: [
                              { $gte: [currentDate, "$startDate"] },
                              { $lte: [currentDate, "$endDate"] }
                            ]
                          },
                          then: "active",
                          else: "pending" // fallback
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          startDate: 1,
          endDate: 1,
          quantity: 1,
          price: 1,
          contractType: '$contractquotationDetail.name',
          contractTypeId: 1,
          createdAt :1,
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
        })
        .match({ $and: query })
        .sort({createdAt :-1})
        .skip(parseInt(offset))
        .limit(parseInt(limit))
        .exec();

      let count: any = await contractModel.aggregate()
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        // Add this stage to extract the active customer address
        .addFields({
          customerAddress: {
            $arrayElemAt: [
              {
                $filter: {
                  input: '$customerDetail.customerAddress',
                  cond: { $eq: ['$$this.status', true] }
                }
              },
              0
            ]
          }
        })
        .lookup({
          from: 'quotations',
          localField: 'contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractquotationDetail',
        })
        .unwind({
          path: '$contractquotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: '$itemDetail.serialNo',
          customerName: '$customerDetail.fullName',
          phone: '$customerDetail.phone',
          email: '$customerDetail.email',
          telePhoneNo: '$customerDetail.alternatePhone',
          address: "$itemDetail.installationAddress",
          customerAddress: 1,
          assignmentDetail: '$itemDetail.assignmentDetail',
          installationDate: '$itemDetail.installationDate',
          // Dynamic status calculation for count query
          status: {
            $cond: {
              if: { $gt: ["$startDate", currentDate] },
              then: "pending",
              else: {
                $cond: {
                  if: { $gt: [currentDate, "$endDate"] },
                  then: "expired",
                  else: {
                    $cond: {
                      if: {
                        $and: [
                          { $lte: [currentDate, "$endDate"] },
                          { $lte: ["$endDate", oneMonthFromNow] }
                        ]
                      },
                      then: "nearExpiry",
                      else: {
                        $cond: {
                          if: {
                            $and: [
                              { $gte: [currentDate, "$startDate"] },
                              { $lte: [currentDate, "$endDate"] }
                            ]
                          },
                          then: "active",
                          else: "pending"
                        }
                      }
                    }
                  }
                }
              }
            }
          },
          startDate: 1,
          endDate: 1,
          quantity: 1,
          price: 1,
          contractType: '$contractquotationDetail.name',
          contractTypeId: 1,
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
        })
        .match({ $and: query })
        .count('totalCount')
        .unwind({ path: '$totalCount', preserveNullAndEmptyArrays: true })
        .exec()

      const [resultPromise] = await Promise.all([result])

      let response = {
        "data": resultPromise,
        "fetchCount": resultPromise.length,
        "totalCount": count[0] && count[0].totalCount ? count[0].totalCount : 0
      }
      return { status: true, code: 200, message: 'data Fetched successfully', data: response }

    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }




}