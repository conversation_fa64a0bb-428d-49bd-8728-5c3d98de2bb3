{"name": "product-microservice", "version": "1.0.0", "description": "", "main": "dist/src/server.js", "types": "dist/src/server.d.ts", "scripts": {"build": "tsc", "start": "npm run build && node dist/src/server.js", "serve": "node dist/src/server.js", "auto": "node --inspect=5858 -r ts-node/register ./src/server.ts", "auto:watch": "nodemon"}, "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "app-root-path": "^3.0.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.7", "bcrypt": "^5.1.1", "body-parser": "^1.20.3", "cluster": "^0.7.7", "cors": "^2.8.5", "express": "^4.21.0", "jsonwebtoken": "^8.5.1", "mongoose": "^7.8.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.15", "uuid": "^11.0.5", "winston": "^3.3.3"}, "devDependencies": {"@types/app-root-path": "^1.2.4", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^8.5.0", "@types/multer": "^1.4.12", "@types/node": "^22.5.0", "@types/nodemailer": "^7.0.1", "typescript": "^5.6.3"}}