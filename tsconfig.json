{
  "compilerOptions": {
    "target": "es2020",              // instead of es5
    "module": "commonjs",
    "declaration": true,
    "moduleResolution": "node",
    "outDir": "./dist/src",
    "strict": true,
    "baseUrl": "src",
    "downlevelIteration": true,
    "rootDirs": ["src"],
    "resolveJsonModule": true,
    "lib": ["es2020", "dom"],        // add modern libs
    "skipLibCheck": true             // skip type checking of declaration files
  },
  "include": ["src"],
  "exclude": ["node_modules", "**/__tests__/*"]
}
