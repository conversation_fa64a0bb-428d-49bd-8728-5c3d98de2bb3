import { Schema, Model, model } from "mongoose";
import { IItemModel } from "./Interfaces/itemInterface";



const addressSchema = new Schema({
  addressLine1: { type: String,   },
  addressLine2: { type: String },
  addressLine3: { type: String },
  city: { type: String },
  state: { type: String },
  country: { type: String },
  pincode: { type: String },
  
});


const assignmentSchema = new Schema({
  organizationStructureId: { type: String},
  userId: { type: String },
  
});



const itemSchema = new Schema(
  {
    modelId: { type: Schema.Types.ObjectId },
    brandId: { type: Schema.Types.ObjectId },
    productId: { type: Schema.Types.ObjectId },
    serialNo: { type: String },
    customerId: { type: Schema.Types.ObjectId, ref: "customer" },
    // customerName: { type: String },
    // contactPerson: { type: String },
    // email: { type: String, lowercase: true, trim: true,required: true },
    // phone: {
    //   countryCode: { type: String,   },
    //   number: { type: String,   }
    // },
    // telePhoneNo: { type: String},
    managerRoleId: { type: Schema.Types.ObjectId, ref: "role" },
    installationAddress: addressSchema,
    assignmentDetail: [assignmentSchema],
    installationDate: { type: Date },
    purchaseDate: { type:Date },
    warrantyStartDate: { type:Date },
    warrantyEndDate: { type:Date },
    createdById: { type: Schema.Types.ObjectId },
    updatedById: { type: Schema.Types.ObjectId }
  },
  {
    timestamps: true,
  }
);

export const itemModel: Model<IItemModel> = model<IItemModel>(
  "item",
  itemSchema
);
