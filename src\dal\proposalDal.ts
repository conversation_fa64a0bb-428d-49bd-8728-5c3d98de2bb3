import { proposalModel } from '../models/proposalModel'
import AppHelper from '../helpers/appHelper'
import { Types } from 'mongoose';


export default class ProposalDal {

  public appHelper: any;
  public packageHelper: any;
  constructor() {
    this.appHelper = new AppHelper;
  }


  public createProposal = async (data: any): Promise<any> => {
    try {

    if (data.quotations && Array.isArray(data.quotations)) {
      const contractTypeIds = data.quotations.map((quotation: any) => quotation.contractTypeId?.toString());
      const uniqueContractTypeIds = new Set(contractTypeIds.filter((id:any) => id)); // Filter out null/undefined
      
      if (contractTypeIds.filter((id:any) => id).length !== uniqueContractTypeIds.size) {
        return { 
          status: false, 
          code: 400, 
          message: "Duplicate contractTypeId found in quotations array", 
          data: null 
        };
      }
    }
       const exists = await proposalModel.findOne({ itemId: new Types.ObjectId(data.itemId)  });
      if (exists) {
        return {
          status: false,
          code: 400,
          message: "item with serial no already present",
          data: null,
        };
      }
      let model = new proposalModel(data)
      let result = await model.save()
      if (result) {
        return { status: true, code: 200, message: "Data saved successfully", data: result._id }
      }
      return { status: false, code: 403, message: `Failed to save data`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }

  public updateProposal = async (data: any, id: any): Promise<any> => {
    try {

      if (data.quotations && typeof data.quotations === 'string') {
        try {
          const parsedQuotations = JSON.parse(data.quotations);
          data.quotations = parsedQuotations; // Extract the quotations array
        } catch (parseError) {
          return {
            status: false,
            code: 400,
            message: 'Invalid quotations format',
            data: parseError
          };
        }
      }

    if (data.quotations && Array.isArray(data.quotations)) {
      const contractTypeIds = data.quotations.map((quotation: any) => quotation.contractTypeId?.toString());
      const uniqueContractTypeIds = new Set(contractTypeIds.filter((id:any) => id)); // Filter out null/undefined
      
      if (contractTypeIds.filter((id:any) => id).length !== uniqueContractTypeIds.size) {
        return { 
          status: false, 
          code: 400, 
          message: "Duplicate contractTypeId found in quotations array", 
          data: null 
        };
      }
    }

      const find = { _id: id }
      const set = { $set: data }
      const options = { upsert: false, new: true }
      let result = await proposalModel.findOneAndUpdate(find, set, options).exec()
      if (result) {
        return { status: true, code: 200, message: 'Data updated successfully', data: result }
      }
      return { status: false, code: 409, message: `Failed to update brand`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }

  }



public getProposalById = async (id: string, type?: string): Promise<any> => {
  try {
    let query: any = [{}]
    query.push({ _id: new Types.ObjectId(id) })

    if (type === "convert") {
      let result: any = await proposalModel.aggregate()
        .match({ $and: query })
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .unwind({
          path: "$quotations",
          preserveNullAndEmptyArrays: true
        })
        .lookup({
          from: 'quotations',
          localField: 'quotations.contractTypeId',  // Assuming quotation field references quotation collection
          foreignField: '_id',
          as: 'quotationData'
        })
        .unwind({
          path: "$quotationData",
          preserveNullAndEmptyArrays: true
        })
        .lookup({
          from: "contractquotations",
          localField: "quotationData.quotationId",
          foreignField: "_id",
          as: "contractQuotationData"
        })
        .unwind({
          path: "$contractQuotationData",
          preserveNullAndEmptyArrays: true
        })
        .addFields({
          "quotations.quotationDetails": {
            name: "$contractQuotationData.name",
            details: "$quotationData.detail",
            _id: "$quotationData._id"
          }
        })
          .group({
          _id: "$_id",
          itemDetail: { $first: "$itemDetail" },
          quotations: { 
            $push: {
              $cond: [
                { $ifNull: ["$quotations", false] },
                "$quotations",
                "$$REMOVE"
              ]
            }
          },
        })
        .project({
          quotations: 1,
          installationDate: '$itemDetail.installationDate',
        })

      if (result) {
        return { status: true, code: 200, message: 'Data fetched successfully', data: result }
      }
      return { status: true, code: 200, message: 'No such document!', data: [] }
    } else {
      let result: any = await proposalModel.aggregate()
        .match({ $and: query })
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        // New stages for quotation details
        .unwind({
          path: "$quotations",
          preserveNullAndEmptyArrays: true
        })
        .lookup({
          from: "quotations",
          localField: "quotations.contractTypeId",
          foreignField: "_id",
          as: "quotationData"
        })
        .unwind({
          path: "$quotationData",
          preserveNullAndEmptyArrays: true
        })
        .lookup({
          from: "contractquotations",
          localField: "quotationData.quotationId",
          foreignField: "_id",
          as: "contractQuotationData"
        })
        .unwind({
          path: "$contractQuotationData",
          preserveNullAndEmptyArrays: true
        })
        .addFields({
          "quotations.quotationDetails": {
            _id: "$quotationData._id",
            name: "$contractQuotationData.name",
            details: "$quotationData.detail"
          }
        })
        // Group back to preserve document structure
        .group({
          _id: "$_id",
          itemDetail: { $first: "$itemDetail" },
          customerDetail: { $first: "$customerDetail" },
          brandDetail: { $first: "$brandDetail" },
          productDetail: { $first: "$productDetail" },
          modelDetail: { $first: "$modelDetail" },
          status: { $first: "$status" },
          subject: { $first: "$subject" },
          termsAndCondition: { $first: "$termsAndCondition" },
          details: { $first: "$details" },
          quotations: { 
            $push: {
              $cond: [
                { $ifNull: ["$quotations", false] },
                "$quotations",
                "$$REMOVE"
              ]
            }
          },
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          itemDetail: 1,
          customerDetail: {
            _id: 1,
            fullName: 1,
            phone: 1,
            email: 1,
            contactPerson: 1,
            telePhoneNo: 1,
            customerAddress: {
              $arrayElemAt: [
                {
                  $filter: {
                    input: "$customerDetail.customerAddress",
                    as: "address",
                    cond: { $eq: ["$$address.status", true] }
                  }
                },
                0
              ]
            },
          },
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
          itemId:'$itemDetail._id',
          status: 1,
          subject: 1,
          termsAndCondition: 1,
          quotations: 1,
          details: 1
        })
        .exec()

      if (result) {
        return { status: true, code: 200, message: 'Data fetched successfully', data: result }
      }
      return { status: true, code: 200, message: 'No such document!', data: [] }
    }
  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message }
  }
}


  public getProposal = async (req: any): Promise<any> => {
    try {
      let offset = req.query.offset ? req.query.offset : 0;
      let limit = req.query.limit ? req.query.limit : 15;
      let query = [{}]
      
      if (req.query.search) {

        let globalValue = String(req.query.search).replace(/([.*+?=^!:${}()|[\]\/\\])/g, '\\$1');
        let match: any = {
          $or: [
            { 'serialNo': { $regex: new RegExp(globalValue), $options: "im" } },
            { 'customerName': { $regex: new RegExp(globalValue), $options: "im" } },
            { 'phone.number': { $regex: new RegExp(globalValue), $options: "im" } },
            { 'brandName': { $regex: new RegExp(globalValue), $options: "im" } },
            { 'productName': { $regex: new RegExp(globalValue), $options: "im" } },
            { 'modelName': { $regex: new RegExp(globalValue), $options: "im" } },

          ]
        }
        query.push(match)
      }
      if (req.query.installationDate) {
        let startDate = new Date(req.query.installationDate);
        startDate.setHours(0, 0, 0, 0);

        let endDate = new Date(req.query.installationDate);
        endDate.setHours(23, 59, 59, 999);

        query.push({
          installationDate: {
            $gte: startDate,
            $lte: endDate
          }
        });
      }
 
      if (req.query.status) {
        query.push({
          status: req.query.status

        })
      }

      query.push({
          active: true
      })

      let result: any = await proposalModel.aggregate()
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'users',
          localField: 'itemDetail.customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: '$itemDetail.serialNo',
          customerName: '$customerDetail.fullName',
          phone: '$customerDetail.phone',
          installationDate: '$itemDetail.installationDate',
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
          modelId:"$modelDetail._id",
          quotations: 1,
          status: 1,
          details: 1,
          active:1,
          itemId:'$itemDetail._id'

        })
        .match({ $and: query })
        .skip(parseInt(offset))
        .limit(parseInt(limit))
        .exec();

      let count: any = await proposalModel.aggregate()
        .lookup({
          from: 'items',
          localField: 'itemId',
          foreignField: '_id',
          as: 'itemDetail',
        })
        .unwind({
          path: '$itemDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'itemDetail.brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'itemDetail.productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'itemDetail.modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: '$itemDetail.serialNo',
          customerName: '$customerDetail.fullName',
          phone: '$customerDetail.phone',
          installationDate: '$itemDetail.installationDate',
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
          status: 1,
          details: 1,
          itemId:'$itemDetail._id',
          active:1

        })
      .match({ $and: query })
      .count('totalCount')
      .unwind({ path: '$totalCount', preserveNullAndEmptyArrays: true })
      .exec()
      const [resultPromise] = await Promise.all([result])
      console.log("vvvv",count)

      let response = {
        "data": resultPromise,
        "fetchCount": resultPromise.length,
        "totalCount": count[0] && count[0].totalCount ? count[0].totalCount : 0
      }
      return { status: true, code: 200, message: 'data Fetched successfully', data: response }

    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }

  }
 



}