import { itemModel } from '../models/itemModel'
import AppHelper from '../helpers/appHelper'
import { connection, mongo } from 'mongoose';
import mongoose from 'mongoose'

export default class ItemDal {

  public appHelper: any;
  public packageHelper: any;
  constructor() {
    this.appHelper = new AppHelper;
  }


public itemCreation = async (data: any): Promise<any> => {
  try {

    if (data.serialNo) {
      const existingItem = await itemModel.findOne(
        { serialNo: data.serialNo }
      ).collation({ locale: "en", strength: 2 });
      if (existingItem) {
        return { status: false, code: 409, message: "Serial Number already exists", data: null };
      }
    }
    if(!data.customerId) {
      return { 
        status: false, 
        code: 400, 
        message: "Customer is required", 
        data: null 
      };
    }
    if(!data.customerAddressId) {
      return { 
        status: false, 
        code: 400, 
        message: "Customer address is required", 
        data: null 
      };
    }
    if (data.customerAddressId) {
      const userModel = connection.db.collection('users');

      // Step 1: Set all customer addresses to false
      const resetResult = await userModel.updateOne(
        { _id:  new mongoose.Types.ObjectId(data.customerId) },
        { $set: { "customerAddress.$[].status": false } }
      );
      
      if (resetResult.matchedCount === 0) {
        return { 
          status: false, 
          code: 404, 
          message: "Customer not found", 
          data: null 
        };
      }

      // Step 2: Set the specific address to true
      const setActiveResult = await userModel.updateOne(
        {
          _id: new mongoose.Types.ObjectId(data.customerId),
          "customerAddress._id": new mongoose.Types.ObjectId(data.customerAddressId)
        },
        {
          $set: {
            "customerAddress.$.status": true
          }
        }
      );

      if (setActiveResult.matchedCount === 0) {
        return { 
          status: false, 
          code: 404, 
          message: "Customer address not found", 
          data: null 
        };
      }

      if (setActiveResult.modifiedCount === 0) {
        return { 
          status: false, 
          code: 400, 
          message: "Failed to update address status", 
          data: null 
        };
      }
    }

    // Create the item
    const model = new itemModel(data);
    const result = await model.save();
    
    if (result) {
      return { 
        status: true, 
        code: 200, 
        message: "Data saved successfully", 
        data: result._id 
      };
    }
    
    return { 
      status: false, 
      code: 403, 
      message: "Failed to save data", 
      data: result 
    };
    
  } catch (error: any) {
    console.error('Item creation error:', error);
    return { 
      code: 500, 
      status: false, 
      message: "Database Error", 
      data: error.message 
    };
  }
}

  public updateItem = async (data: any, id: any): Promise<any> => {
    try {

    if (data.serialNo) {
      // Check if another item with the same serialNo exists
    const existingItem = await itemModel.findOne(
      { 
        serialNo: data.serialNo, 
        _id: { $ne: id } 
      }
    ).collation({ locale: "en", strength: 2 });
      if (existingItem) {
        return {
          status: false,
          code: 409,
          message: "Serial Number already exists",
          data: null
        };
      }
    }
      const find = { _id: id }
      const set = { $set: data }
      const options = { upsert: false, new: true }
      let result = await itemModel.findOneAndUpdate(find, set, options).exec()
      if (result) {
        return { status: true, code: 200, message: 'Data updated successfully', data: result }
      }
      return { status: false, code: 409, message: `Failed to update brand`, data: result }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }

  }



  public getItemById = async (id: string): Promise<any> => {
    try {
      let result: any = await itemModel.aggregate()
        .match({ _id: new mongoose.Types.ObjectId(id) })
        .lookup({
          from: 'users',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        // Add lookups for assignmentDetail
        .unwind({
          path: '$assignmentDetail',
          preserveNullAndEmptyArrays: true
        })
        .lookup({
          from: 'users',
          localField: 'assignmentDetail.userId',
          foreignField: '_id',
          as: 'assignmentUserDetail',
        })
        .unwind({
          path: '$assignmentUserDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'organizationstructures',
          localField: 'assignmentDetail.organizationStructureId',
          foreignField: '_id',
          as: 'assignmentOrgDetail',
        })
        .unwind({
          path: '$assignmentOrgDetail',
          preserveNullAndEmptyArrays: true,
        })
        // Group to reconstruct the document
        .group({
          _id: '$_id',
          serialNo: { $first: '$serialNo' },
          customerId: { $first: '$customerId' },
          customerDetail: { $first: '$customerDetail' },
          brandDetail: { $first: '$brandDetail' },
          productDetail: { $first: '$productDetail' },
          modelDetail: { $first: '$modelDetail' },
          installationAddress: { $first: '$installationAddress' },
          installationDate: { $first: '$installationDate' },
          purchaseDate: { $first: '$purchaseDate' },
          warrantyStartDate: { $first: '$warrantyStartDate' },
          warrantyEndDate: { $first: '$warrantyEndDate' },
          assignmentDetail: {
            $push: {
              $cond: [
                { $ifNull: ['$assignmentDetail.userId', false] },
                {
                  userId: '$assignmentDetail.userId',
                  userName: '$assignmentUserDetail.fullName',
                  organizationStructureId: '$assignmentDetail.organizationStructureId',
                  unitName: '$assignmentOrgDetail.unitName',
                  // Add any other fields from assignmentDetail that you need
                },
                '$$REMOVE'
              ]
            }
          }
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: 1,
          customerName: "$customerDetail.fullName",
          customerId: 1,
          contactPerson: "$customerDetail.contactPerson",
          email: "$customerDetail.email",
          phone: "$customerDetail.phone",
          alternatePhone: "$customerDetail.alternatePhone",
          customerAddress: {
            $arrayElemAt: [
              {
                $filter: {
                  input: "$customerDetail.customerAddress",
                  as: "address",
                  cond: { $eq: ["$$address.status", true] }
                }
              },
              0
            ]
          },
          installationAddress: 1,
          assignmentDetail: {
            $cond: [
              { $eq: [{ $size: { $ifNull: ['$assignmentDetail', []] } }, 0] },
              [],
              '$assignmentDetail'
            ]
          },
          installationDate: 1,
          purchaseDate: 1,
          warrantyStartDate: 1,
          warrantyEndDate: 1,
          status: {
            $cond: {
              if: {
                $or: [
                  { $eq: ["$warrantyStartDate", null] },
                  { $gt: ["$warrantyStartDate", new Date()] }
                ]
              },
              then: "pending",
              else: {
                $cond: {
                  if: {
                    $and: [
                      { $lte: ["$warrantyStartDate", new Date()] },
                      { $gte: ["$warrantyEndDate", new Date()] }
                    ]
                  },
                  then: "active",
                  else: "expired"
                }
              }
            }
          },
          brand: {
            _id: '$brandDetail._id',
            name: '$brandDetail.brandName',
          },
          product: {
            _id: '$productDetail._id',
            name: '$productDetail.productTypeName',
          },
          model: {
            _id: '$modelDetail._id',
            name: '$modelDetail.modelName',
          },
        })

      if (result.length > 0) {
        return { status: true, code: 200, message: 'Data fetched successfully', data: result[0] }
      }
      return { status: true, code: 200, message: 'No such document!', data: [] }
    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }
  }


  public getItem = async (req: any): Promise<any> => {
    try {
      let offset = req.query.offset ? req.query.offset : 0;
      let limit = req.query.limit ? req.query.limit : 15;
      let query = [{}]
      if (req.query.type == "all") {
        let result = await this.getAllItem(req)
        return { status: true, code: 200, message: 'Data Fetched successfully', data: result.data }

      }

      let querySearch: any = [{}]
      if (req.query.search) {
        let globalValue = String(req.query.search).replace(/([.*+?=^!:${}()|[\]\/\\])/g, '\\$1');
        let match: any;

        // Conditional search based on type
        if (req.query.type === "proposal") {
          // Only search by serialNo for proposal type
          match = {
            'serialNo': { $regex: new RegExp(globalValue), $options: "im" }
          }
        } else {
          // Default search across multiple fields
          match = {
            $or: [
              { 'serialNo': { $regex: new RegExp(globalValue), $options: "im" } },
              { 'customerName': { $regex: new RegExp(globalValue), $options: "im" } },
              { 'phone.number': { $regex: new RegExp(globalValue), $options: "im" } },
              { 'brandName': { $regex: new RegExp(globalValue), $options: "im" } },
              { 'productName': { $regex: new RegExp(globalValue), $options: "im" } },
              { 'modelName': { $regex: new RegExp(globalValue), $options: "im" } }
            ]
          }
        }
        querySearch.push(match)
      }

      if (req.query.warrantyStartDate) {
        let startDate = new Date(req.query.warrantyStartDate);
        startDate.setHours(0, 0, 0, 0);
        let endDate = new Date(req.query.warrantyStartDate);
         endDate.setHours(23, 59, 59, 999);
        query.push({
          warrantyStartDate: {
            $gte: startDate,
            $lte: endDate
          }
        });
      }

      if (req.query.warrantyEndDate) {
        let startDate = new Date(req.query.warrantyEndDate);
        startDate.setHours(0, 0, 0, 0);
        let endDate = new Date(req.query.warrantyEndDate);
        endDate.setHours(23, 59, 59, 999);
      
        query.push({
          warrantyEndDate: { 
             $gte: startDate,
             $lte: endDate
           }
        });
      }

      if (req.query.status) {
        querySearch.push({
          status: req.query.status

        })
      }

      console.log("querySearch ", query)
      let result: any = itemModel.aggregate()
        .match({ $and: query })
        .lookup({
          from: 'users',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'brands',
          localField: 'brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
        .unwind({
          path: '$modelDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contracts',
          localField: '_id',
          foreignField: 'itemId',
          as: 'contractDetail',
        })
        .unwind({
          path: '$contractDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'quotations',
          localField: 'contractDetail.contractTypeId',
          foreignField: '_id',
          as: 'quotationDetail',
        })
        .unwind({
          path: '$quotationDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'contractquotations',
          localField: 'quotationDetail.quotationId',
          foreignField: '_id',
          as: 'contractQuotationDetails'
        })
        .unwind({ path: '$contractQuotationDetails', preserveNullAndEmptyArrays: true })
        .unwind({ path: '$contractQuotationDetails', preserveNullAndEmptyArrays: true })
        // Add these new lookups for assignmentDetail
        .unwind({ path: '$assignmentDetail', preserveNullAndEmptyArrays: true })
        .lookup({
          from: 'users',
          localField: 'assignmentDetail.userId',
          foreignField: '_id',
          as: 'assignmentUserDetail',
        })
        .unwind({
          path: '$assignmentUserDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'organizationstructures',
          localField: 'assignmentDetail.organizationStructureId',
          foreignField: '_id',
          as: 'assignmentOrgDetail',
        })
        .unwind({
          path: '$assignmentOrgDetail',
          preserveNullAndEmptyArrays: true,
        })
        .group({
          _id: '$_id',
          serialNo: { $first: '$serialNo' },
          customerId: { $first: '$customerId' },
          brandId: { $first: '$brandId' },
          productId: { $first: '$productId' },
          modelId: { $first: '$modelId' },
          customerDetail: { $first: '$customerDetail' },
          brandDetail: { $first: '$brandDetail' },
          productDetail: { $first: '$productDetail' },
          modelDetail: { $first: '$modelDetail' },
          contractDetail: { $first: '$contractDetail' },
          quotationDetail: { $first: '$quotationDetail' },
          contractQuotationDetails: { $first: '$contractQuotationDetails' },
          installationAddress: { $first: '$installationAddress' },
          installationDate: { $first: '$installationDate' },
          purchaseDate: { $first: '$purchaseDate' },
          warrantyStartDate: { $first: '$warrantyStartDate' },
          warrantyEndDate: { $first: '$warrantyEndDate' },
          createdAt: { $first: '$createdAt' },
          assignmentDetail: {
            $push: {
              userId: '$assignmentDetail.userId',
              userName: '$assignmentUserDetail.fullName',
              organizationStructureId: '$assignmentDetail.organizationStructureId',
              unitName: '$assignmentOrgDetail.unitName',
              // Add any other fields from assignmentDetail you need
            }
          }
        })
        .collation({ locale: "en" })
        .project({
          _id: 1,
          serialNo: 1,
          customerName: "$customerDetail.fullName",
          contactPerson: "$customerDetail.contactPerson",
          email: "$customerDetail.email",
          phone: "$customerDetail.phone",
          telePhoneNo: "$customerDetail.alternatePhone",
          customerAddress: {
            $arrayElemAt: [
              {
                $filter: {
                  input: "$customerDetail.customerAddress",
                  as: "address",
                  cond: { $eq: ["$$address.status", true] }
                }
              },
              0
            ]
          },
          installationAddress: 1,
          assignmentDetail: 1, // This now includes userName and unitName
          installationDate: 1,
          purchaseDate: 1,
          contractStartDate: "$contractDetail.startDate",
          contractEndDate: "$contractDetail.endDate",
          contractType: "$contractQuotationDetails.name",
          warrantyStartDate: 1,
          warrantyEndDate: 1,
          status: {
            $cond: {
              if: {
                $or: [
                  { $eq: ["$warrantyStartDate", null] },
                  { $gt: ["$warrantyStartDate", new Date()] }
                ]
              },
              then: "pending",
              else: {
                $cond: {
                  if: {
                    $and: [
                      { $lte: ["$warrantyStartDate", new Date()] },
                      { $gte: ["$warrantyEndDate", new Date()] }
                    ]
                  },
                  then: "active",
                  else: "expired"
                }
              }
            }
          },
          brandName: '$brandDetail.brandName',
          productName: '$productDetail.productTypeName',
          modelName: '$modelDetail.modelName',
          modelId: "$modelDetail._id",
          createdAt: 1
        })
        .match({ $and: querySearch })
        .sort({ createdAt: -1 })
        .skip(parseInt(offset))
        .limit(parseInt(limit))
        .exec();

      let count: any = await itemModel.aggregate()
        .match({ $and: query })
        .lookup({
          from: 'brands',
          localField: 'brandId',
          foreignField: '_id',
          as: 'brandDetail',
        })
        .unwind({
          path: '$brandDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'products',
          localField: 'productId',
          foreignField: '_id',
          as: 'productDetail',
        })
        .unwind({
          path: '$productDetail',
          preserveNullAndEmptyArrays: true,
        })
        .lookup({
          from: 'models',
          localField: 'modelId',
          foreignField: '_id',
          as: 'modelDetail',
        })
      .unwind({
        path: '$productDetail',
        preserveNullAndEmptyArrays: true,
      })
     .lookup({
          from: 'users',
          localField: 'customerId',
          foreignField: '_id',
          as: 'customerDetail',
        })
        .unwind({
          path: '$customerDetail',
          preserveNullAndEmptyArrays: true,
        })
      .project({
        _id: 1,
        serialNo: 1,
        customerName: "$customerDetail.fullName",
        status: {
          $cond: {
            if: {
              $or: [
                { $eq: ["$warrantyStartDate", null] },
                { $gt: ["$warrantyStartDate", new Date()] }
              ]
            },
            then: "pending",
            else: {
              $cond: {
                if: {
                  $and: [
                    { $lte: ["$warrantyStartDate", new Date()] },
                    { $gte: ["$warrantyEndDate", new Date()] }
                  ]
                },
                then: "active",
                else: "expired"
              }
            }
          }
        },
        contactPerson: "$customerDetail.contactPerson",
        email: "$customerDetail.email",
        phone: "$customerDetail.phone",
        telePhoneNo: "$customerDetail.alternatePhone",
        customerAddress: {
          $filter: {
            input: "$customerDetail.customerAddress",
            as: "address",
            cond: { $eq: ["$$address.status", true] }
          }
        },
        installationAddress: 1,
        assignmentDetail: 1,
        installationDate: 1,
        purchaseDate: 1,
        warrantyStartDate: 1,
        warrantyEndDate: 1,
        modelId:"$modelDetail._id",
        brandName: '$brandDetail.brandName',
        productName: '$productDetail.productTypeName',
        modelName: '$modelDetail.modelName',
      })
      .match({ $and: querySearch })
      .count('totalCount')
      .unwind({ path: '$totalCount', preserveNullAndEmptyArrays: true })
      .exec()
      const [resultPromise] = await Promise.all([result])

      let response = {
        "data": resultPromise,
        "fetchCount": resultPromise.length,
        "totalCount": count[0] && count[0].totalCount ? count[0].totalCount : 0
      }
      return { status: true, code: 200, message: 'data Fetched successfully', data: response }

    } catch (error: any) {
      return { code: 500, status: false, message: `Database-Error`, data: error.message }
    }

  }

  public getAllItem = async (req: any): Promise<any> => {
  try {
    const query: any = [{}, { active: true }];
    const now = new Date();

    const result = itemModel.aggregate([
      {
        $match: { $and: query }
      },
      {
        $addFields: {
          status: {
            $cond: {
              if: { $eq: ["$warrantyStartDate", null] },
              then: "pending",
              else: {
                $cond: {
                  if: {
                    $and: [
                      { $lte: ["$warrantyStartDate", now] },
                      { $gte: ["$warrantyEndDate", now] }
                    ]
                  },
                  then: "active",
                  else: "expired"
                }
              }
            }
          }
        }
      }
    ])
    .collation({ locale: "en" })
    .exec();

    const [resultPromise] = await Promise.all([result]);
    return { status: true, code: 200, message: 'Data Fetched successfully', data: resultPromise };

  } catch (error: any) {
    return { code: 500, status: false, message: `Database-Error`, data: error.message };
  }
}




}